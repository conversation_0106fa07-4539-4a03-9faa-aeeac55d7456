2025-06-25 00:49:54 [INFO] l1_manager.py:173 - Processing cluster with 10 memories
2025-06-25 00:49:54 [WARNING] shade_generator.py:401 - shade_info_list: []
2025-06-25 00:49:54 [WARNING] shade_generator.py:402 - old_memory_list: []
2025-06-25 00:49:54 [WARNING] shade_generator.py:403 - new_memory_list: [<lpm_kernel.L1.bio.Note object at 0xffff3e9bd8e0>, <lpm_kernel.L1.bio.Note object at 0xffff3f46eae0>, <lpm_kernel.L1.bio.Note object at 0xffff3f41fe30>, <lpm_kernel.L1.bio.Note object at 0xffff3eb43560>, <lpm_kernel.L1.bio.Note object at 0xffff3063f200>, <lpm_kernel.L1.bio.Note object at 0xffff3f4ed4c0>, <lpm_kernel.L1.bio.Note object at 0xffff3f46f530>, <lpm_kernel.L1.bio.Note object at 0xffff3f46f6b0>, <lpm_kernel.L1.bio.Note object at 0xffff3f46df40>, <lpm_kernel.L1.bio.Note object at 0xffff3065ac60>]
2025-06-25 00:49:54 [INFO] shade_generator.py:406 - Shades initial Process! Current shade have 10 memories!
2025-06-25 00:50:02 [INFO] shade_generator.py:262 - Shade Generate Result: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:02 [ERROR] shade_generator.py:203 - No Json Found: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:02 [ERROR] shade_generator.py:226 - Failed to parse the shade generate result: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:02 [INFO] l1_manager.py:173 - Processing cluster with 20 memories
2025-06-25 00:50:03 [WARNING] shade_generator.py:401 - shade_info_list: []
2025-06-25 00:50:03 [WARNING] shade_generator.py:402 - old_memory_list: []
2025-06-25 00:50:03 [WARNING] shade_generator.py:403 - new_memory_list: [<lpm_kernel.L1.bio.Note object at 0xffff3f4ee660>, <lpm_kernel.L1.bio.Note object at 0xffff3f461fa0>, <lpm_kernel.L1.bio.Note object at 0xffff3eb435f0>, <lpm_kernel.L1.bio.Note object at 0xffff3f46cd70>, <lpm_kernel.L1.bio.Note object at 0xffff3eb42cc0>, <lpm_kernel.L1.bio.Note object at 0xffff30afedb0>, <lpm_kernel.L1.bio.Note object at 0xffff3f46d3d0>, <lpm_kernel.L1.bio.Note object at 0xffff338ba3f0>, <lpm_kernel.L1.bio.Note object at 0xffff306a31d0>, <lpm_kernel.L1.bio.Note object at 0xffff338ba450>, <lpm_kernel.L1.bio.Note object at 0xffff30af9700>, <lpm_kernel.L1.bio.Note object at 0xffff3f46fad0>, <lpm_kernel.L1.bio.Note object at 0xffff3f44bf80>, <lpm_kernel.L1.bio.Note object at 0xffff3f487b00>, <lpm_kernel.L1.bio.Note object at 0xffff306bfad0>, <lpm_kernel.L1.bio.Note object at 0xffff30afd940>, <lpm_kernel.L1.bio.Note object at 0xffff3eb42d20>, <lpm_kernel.L1.bio.Note object at 0xffff3f46e690>, <lpm_kernel.L1.bio.Note object at 0xffff306bf170>, <lpm_kernel.L1.bio.Note object at 0xffff3063e540>]
2025-06-25 00:50:03 [INFO] shade_generator.py:406 - Shades initial Process! Current shade have 20 memories!
2025-06-25 00:50:08 [INFO] shade_generator.py:262 - Shade Generate Result: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:08 [ERROR] shade_generator.py:203 - No Json Found: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:08 [ERROR] shade_generator.py:226 - Failed to parse the shade generate result: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:08 [INFO] l1_manager.py:173 - Processing cluster with 21 memories
2025-06-25 00:50:08 [WARNING] shade_generator.py:401 - shade_info_list: []
2025-06-25 00:50:08 [WARNING] shade_generator.py:402 - old_memory_list: []
2025-06-25 00:50:08 [WARNING] shade_generator.py:403 - new_memory_list: [<lpm_kernel.L1.bio.Note object at 0xffff3f45ba70>, <lpm_kernel.L1.bio.Note object at 0xffff3e9bc260>, <lpm_kernel.L1.bio.Note object at 0xffff338b9130>, <lpm_kernel.L1.bio.Note object at 0xffff3f46e720>, <lpm_kernel.L1.bio.Note object at 0xffff3f64b200>, <lpm_kernel.L1.bio.Note object at 0xffff3e9bc710>, <lpm_kernel.L1.bio.Note object at 0xffff3f64abd0>, <lpm_kernel.L1.bio.Note object at 0xffff3f46fc80>, <lpm_kernel.L1.bio.Note object at 0xffff3f44be60>, <lpm_kernel.L1.bio.Note object at 0xffff3eb40f20>, <lpm_kernel.L1.bio.Note object at 0xffff30afc8f0>, <lpm_kernel.L1.bio.Note object at 0xffff30afbe30>, <lpm_kernel.L1.bio.Note object at 0xffff338b8410>, <lpm_kernel.L1.bio.Note object at 0xffff30afade0>, <lpm_kernel.L1.bio.Note object at 0xffff3f46fc50>, <lpm_kernel.L1.bio.Note object at 0xffff3f46cf80>, <lpm_kernel.L1.bio.Note object at 0xffff30aff500>, <lpm_kernel.L1.bio.Note object at 0xffff306d64b0>, <lpm_kernel.L1.bio.Note object at 0xffff338bb9e0>, <lpm_kernel.L1.bio.Note object at 0xffff30662420>, <lpm_kernel.L1.bio.Note object at 0xffff306636e0>]
2025-06-25 00:50:08 [INFO] shade_generator.py:406 - Shades initial Process! Current shade have 21 memories!
2025-06-25 00:50:14 [INFO] shade_generator.py:262 - Shade Generate Result: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:14 [ERROR] shade_generator.py:203 - No Json Found: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:14 [ERROR] shade_generator.py:226 - Failed to parse the shade generate result: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:14 [INFO] l1_manager.py:173 - Processing cluster with 9 memories
2025-06-25 00:50:14 [WARNING] shade_generator.py:401 - shade_info_list: []
2025-06-25 00:50:14 [WARNING] shade_generator.py:402 - old_memory_list: []
2025-06-25 00:50:14 [WARNING] shade_generator.py:403 - new_memory_list: [<lpm_kernel.L1.bio.Note object at 0xffff3f46fb60>, <lpm_kernel.L1.bio.Note object at 0xffff3f46f0b0>, <lpm_kernel.L1.bio.Note object at 0xffff30afe480>, <lpm_kernel.L1.bio.Note object at 0xffff3f461dc0>, <lpm_kernel.L1.bio.Note object at 0xffff3f46cb00>, <lpm_kernel.L1.bio.Note object at 0xffff3f4761b0>, <lpm_kernel.L1.bio.Note object at 0xffff3f461f10>, <lpm_kernel.L1.bio.Note object at 0xffff306d4ce0>, <lpm_kernel.L1.bio.Note object at 0xffff3f46f470>]
2025-06-25 00:50:14 [INFO] shade_generator.py:406 - Shades initial Process! Current shade have 9 memories!
2025-06-25 00:50:19 [INFO] shade_generator.py:262 - Shade Generate Result: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:19 [ERROR] shade_generator.py:203 - No Json Found: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:19 [ERROR] shade_generator.py:226 - Failed to parse the shade generate result: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:19 [INFO] l1_manager.py:173 - Processing cluster with 10 memories
2025-06-25 00:50:19 [WARNING] shade_generator.py:401 - shade_info_list: []
2025-06-25 00:50:19 [WARNING] shade_generator.py:402 - old_memory_list: []
2025-06-25 00:50:19 [WARNING] shade_generator.py:403 - new_memory_list: [<lpm_kernel.L1.bio.Note object at 0xffff3f46e330>, <lpm_kernel.L1.bio.Note object at 0xffff3f4efaa0>, <lpm_kernel.L1.bio.Note object at 0xffff3eb42630>, <lpm_kernel.L1.bio.Note object at 0xffff3eb42060>, <lpm_kernel.L1.bio.Note object at 0xffff30aff080>, <lpm_kernel.L1.bio.Note object at 0xffff3eb41c70>, <lpm_kernel.L1.bio.Note object at 0xffff338bbb60>, <lpm_kernel.L1.bio.Note object at 0xffff3f46fa70>, <lpm_kernel.L1.bio.Note object at 0xffff3f46fe30>, <lpm_kernel.L1.bio.Note object at 0xffff3063c2f0>]
2025-06-25 00:50:19 [INFO] shade_generator.py:406 - Shades initial Process! Current shade have 10 memories!
2025-06-25 00:50:25 [INFO] shade_generator.py:262 - Shade Generate Result: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:25 [ERROR] shade_generator.py:203 - No Json Found: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:25 [ERROR] shade_generator.py:226 - Failed to parse the shade generate result: I'm excited to help! Please provide me with the user's memories (Personal Creations and Online Excerpts), and I'll analyze them to determine their interest or hobby. Then, I'll generate the requested content based on that interest.

Please note that I'll use English language in the generated output, but retain original language for special proper nouns as per your request.

Please provide me with the memories, and I'll get started!
2025-06-25 00:50:25 [INFO] l1_manager.py:173 - Processing cluster with 4 memories
2025-06-25 00:50:25 [WARNING] shade_generator.py:401 - shade_info_list: []
2025-06-25 00:50:25 [WARNING] shade_generator.py:402 - old_memory_list: []
2025-06-25 00:50:25 [WARNING] shade_generator.py:403 - new_memory_list: [<lpm_kernel.L1.bio.Note object at 0xffff338bb230>, <lpm_kernel.L1.bio.Note object at 0xffff3f46fdd0>, <lpm_kernel.L1.bio.Note object at 0xffff30aff6e0>, <lpm_kernel.L1.bio.Note object at 0xffff3f46da00>]
2025-06-25 00:50:25 [INFO] shade_generator.py:406 - Shades initial Process! Current shade have 4 memories!
2025-06-25 00:50:37 [INFO] shade_generator.py:262 - Shade Generate Result: 
2025-06-25 00:50:37 [ERROR] shade_generator.py:203 - No Json Found: 
2025-06-25 00:50:37 [ERROR] shade_generator.py:226 - Failed to parse the shade generate result: 
2025-06-25 00:50:37 [INFO] l1_manager.py:135 - Generated 0 shades
2025-06-25 00:50:37 [INFO] shade_generator.py:657 - Built merge_decision_message: [{'role': 'system', 'content': 'You are an AI assistant specialized in analyzing and merging similar user identity shades. Your task involves three steps:\n\n1. First, analyze each shade\'s core characteristics based on its:\n   - Name\n   - Aspect\n   - Description (Third View)\n   - Content (Third View)\n\n2. Then, identify which shades can be merged by:\n   - Looking for semantic similarities in core characteristics\n   - Identify shades that can be turned into more complete content when merged \n   - Finding overlapping interests or behaviors\n   - Identifying complementary traits\n   - Evaluating the context and meaning\n\n3. Finally, output mergeable shade groups where:\n   - Each shade can only appear in one merge group\n   - Multiple merge groups are allowed\n   - Each merge group must contain at least 2 shades\n   - If no shades need to be merged, return an empty array []\n\nYour output must be a JSON array of arrays, where each inner array contains the IDs of shades that can be merged. For example:\n[\n    ["shade_id1", "shade_id2"],\n    ["shade_id3", "shade_id4", "shade_id5"],\n    ["shade_id6", "shade_id7"]\n]\n\nOr if no shades need to be merged:\n[]\n\nImportant:\n- Only output the JSON array, no additional text\n- Ensure each shade ID appears only once across all groups\n- Each group must contain at least 2 shade IDs\n- Only suggest merging when there is strong evidence of similarity or redundancy'}, {'role': 'user', 'content': 'Shades List:\n\n'}, {'role': 'system', 'content': 'User preferred to use en language, you should use the language in the appropriate fields during the generation process, but retain the original language for some special proper nouns.'}]
2025-06-25 00:50:39 [INFO] shade_generator.py:661 - Shade Merge Decision Result: Shades List:
2025-06-25 00:50:39 [ERROR] shade_generator.py:630 - No Json Found: Shades List:
2025-06-25 00:50:39 [INFO] shade_generator.py:665 - Parsed merge_shade_list: None
2025-06-25 00:50:39 [INFO] l1_manager.py:137 - Merged shades success: True
2025-06-25 00:50:39 [INFO] l1_manager.py:138 - Number of merged shades: 0
2025-06-25 00:50:43 [INFO] l1_manager.py:151 - Generated global biography: <lpm_kernel.L1.bio.Bio object at 0xffff30984c20>
2025-06-25 00:50:43 [INFO] l1_manager.py:158 - L1 generation completed successfully
2025-06-25 00:50:43 [INFO] trainprocess_service.py:246 - Successfully generated L1 data and biography
2025-06-25 00:50:43 [INFO] trainprocess_service.py:254 - Biography generation completed successfully
2025-06-25 00:50:43 [INFO] trainprocess_service.py:1116 - Step generate_biography completed successfully
2025-06-25 00:50:43 [INFO] trainprocess_service.py:1099 - Starting step: map_your_entity_network
2025-06-25 00:50:43 [INFO] trainprocess_service.py:297 - Starting entity network mapping...
2025-06-25 00:50:43 [INFO] trainprocess_service.py:463 - Preparing L2 data...
2025-06-25 00:50:43 [INFO] trainprocess_service.py:475 - Topics data not found, generating it...
2025-06-25 00:50:43 [INFO] trainprocess_service.py:482 - Notes not found, preparing them...
2025-06-25 00:50:43 [INFO] document_service.py:355 - list_documents len: 100
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 1
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 1
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 1 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 2
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 2
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 2 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 3
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 3
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 3 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 4
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 4
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 4 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 5
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 5
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 5 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 6
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 6
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 6 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 7
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 7
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 7 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 8
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 8
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 8 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 9
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 9
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 9 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 10
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 10
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 10 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 64 chunks for document 11
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 11
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 12
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 13
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 14
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 11 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 12
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 15
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 12 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 13
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 16
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 13 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 14
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 17
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 14 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 15
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 18
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 15 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 16
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 19
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 16 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 17
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 20
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 17 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 18
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 21
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 18 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 19
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 22
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 19 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 20
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 23
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 20 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 21
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 24
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 21 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 22
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 25
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 22 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 23
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 26
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 23 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 24
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 27
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 24 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 32 chunks for document 25
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 28
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 29
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 25 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 26
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 30
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 26 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 27
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 31
2025-06-25 00:50:43 [INFO] document_service.py:364 - success getting L0 data for document 27 success
2025-06-25 00:50:43 [INFO] document_service.py:310 - Found 16 chunks for document 28
2025-06-25 00:50:43 [WARNING] embedding_service.py:260 - No embedding found for chunk 32
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 28 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 29
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 33
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 29 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 30
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 34
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 30 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 31
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 35
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 31 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 32
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 36
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 32 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 15 chunks for document 33
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 37
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 33 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 34
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 38
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 34 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 35
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 39
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 35 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 36
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 40
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 36 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 37
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 41
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 37 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 38
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 42
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 38 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 39
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 43
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 39 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 40
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 44
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 40 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 41
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 45
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 41 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 42
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 46
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 42 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 43
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 47
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 43 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 64 chunks for document 44
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 48
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 49
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 50
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 51
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 44 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 45
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 52
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 45 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 48 chunks for document 46
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 53
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 54
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 55
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 46 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 47
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 56
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 47 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 48
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 57
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 48 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 32 chunks for document 49
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 58
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 59
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 49 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 50
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 60
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 50 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 51
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 61
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 51 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 52
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 62
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 52 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 32 chunks for document 53
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 63
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 64
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 53 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 32 chunks for document 54
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 65
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 66
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 54 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 55
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 67
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 55 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 56
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 68
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 56 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 57
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 69
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 57 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 58
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 70
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 58 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 32 chunks for document 59
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 71
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 72
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 59 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 60
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 73
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 60 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 48 chunks for document 61
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 74
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 75
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 76
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 61 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 62
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 77
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 62 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 63
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 78
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 63 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 64
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 79
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 64 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 65
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 80
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 65 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 66
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 81
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 66 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 67
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 82
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 67 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 68
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 83
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 68 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 112 chunks for document 69
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 84
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 85
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 86
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 87
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 88
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 89
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 90
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 69 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 70
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 91
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 70 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 71
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 92
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 71 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 72
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 93
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 72 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 73
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 94
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 73 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 74
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 95
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 74 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 75
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 96
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 75 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 76
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 97
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 76 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 77
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 98
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 77 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 78
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 99
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 78 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 32 chunks for document 79
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 100
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 101
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 79 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 80
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 102
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 80 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 81
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 103
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 81 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 82
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 104
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 82 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 83
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 105
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 83 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 84
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 106
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 84 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 85
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 107
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 85 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 86
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 108
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 86 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 87
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 109
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 87 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 88
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 110
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 88 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 89
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 111
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 89 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 90
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 112
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 90 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 91
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 113
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 91 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 92
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 114
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 92 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 93
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 115
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 93 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 94
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 116
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 94 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 32 chunks for document 95
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 117
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 118
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 95 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 96
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 119
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 96 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 97
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 120
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 97 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 98
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 121
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 98 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 99
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 122
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 99 success
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 100
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 123
2025-06-25 00:50:44 [INFO] document_service.py:364 - success getting L0 data for document 100 success
2025-06-25 00:50:44 [INFO] trainprocess_service.py:484 - list_documents_with_l0 len: 100
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 1
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 1
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 2
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 2
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 3
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 3
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 4
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 4
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 5
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 5
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 6
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 6
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 7
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 7
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 8
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 8
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 9
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 9
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 16 chunks for document 10
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 10
2025-06-25 00:50:44 [INFO] document_service.py:310 - Found 64 chunks for document 11
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 11
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 12
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 13
2025-06-25 00:50:44 [WARNING] embedding_service.py:260 - No embedding found for chunk 14
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 12
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 15
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 13
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 16
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 14
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 17
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 15
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 18
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 16
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 19
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 17
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 20
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 18
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 21
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 19
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 22
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 20
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 23
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 21
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 24
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 22
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 25
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 23
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 26
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 24
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 27
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 32 chunks for document 25
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 28
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 29
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 26
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 30
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 27
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 31
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 28
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 32
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 29
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 33
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 30
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 34
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 31
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 35
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 32
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 36
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 15 chunks for document 33
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 37
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 34
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 38
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 35
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 39
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 36
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 40
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 37
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 41
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 38
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 42
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 39
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 43
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 40
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 44
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 41
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 45
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 42
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 46
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 43
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 47
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 64 chunks for document 44
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 48
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 49
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 50
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 51
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 45
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 52
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 48 chunks for document 46
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 53
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 54
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 55
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 47
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 56
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 48
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 57
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 32 chunks for document 49
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 58
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 59
2025-06-25 00:50:45 [INFO] document_service.py:310 - Found 16 chunks for document 50
2025-06-25 00:50:45 [WARNING] embedding_service.py:260 - No embedding found for chunk 60
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 51
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 61
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 52
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 62
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 32 chunks for document 53
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 63
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 64
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 32 chunks for document 54
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 65
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 66
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 55
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 67
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 56
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 68
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 57
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 69
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 58
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 70
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 32 chunks for document 59
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 71
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 72
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 60
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 73
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 48 chunks for document 61
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 74
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 75
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 76
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 62
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 77
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 63
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 78
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 64
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 79
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 65
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 80
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 66
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 81
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 67
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 82
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 68
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 83
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 112 chunks for document 69
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 84
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 85
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 86
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 87
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 88
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 89
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 90
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 70
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 91
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 71
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 92
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 72
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 93
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 73
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 94
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 74
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 95
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 75
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 96
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 76
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 97
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 77
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 98
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 78
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 99
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 32 chunks for document 79
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 100
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 101
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 80
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 102
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 81
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 103
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 82
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 104
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 83
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 105
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 84
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 106
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 85
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 107
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 86
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 108
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 87
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 109
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 88
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 110
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 89
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 111
2025-06-25 00:50:46 [INFO] document_service.py:310 - Found 16 chunks for document 90
2025-06-25 00:50:46 [WARNING] embedding_service.py:260 - No embedding found for chunk 112
2025-06-25 00:50:47 [INFO] document_service.py:310 - Found 16 chunks for document 91
2025-06-25 00:50:47 [WARNING] embedding_service.py:260 - No embedding found for chunk 113
2025-06-25 00:50:47 [INFO] document_service.py:310 - Found 16 chunks for document 92
2025-06-25 00:50:47 [WARNING] embedding_service.py:260 - No embedding found for chunk 114
2025-06-25 00:50:47 [INFO] document_service.py:310 - Found 16 chunks for document 93
2025-06-25 00:50:47 [WARNING] embedding_service.py:260 - No embedding found for chunk 115
2025-06-25 00:50:47 [INFO] document_service.py:310 - Found 16 chunks for document 94
2025-06-25 00:50:47 [WARNING] embedding_service.py:260 - No embedding found for chunk 116
2025-06-25 00:50:47 [INFO] document_service.py:310 - Found 32 chunks for document 95
2025-06-25 00:50:47 [WARNING] embedding_service.py:260 - No embedding found for chunk 117
2025-06-25 00:50:47 [WARNING] embedding_service.py:260 - No embedding found for chunk 118
2025-06-25 00:50:47 [INFO] document_service.py:310 - Found 16 chunks for document 96
2025-06-25 00:50:47 [WARNING] embedding_service.py:260 - No embedding found for chunk 119
2025-06-25 00:50:47 [INFO] document_service.py:310 - Found 16 chunks for document 97
2025-06-25 00:50:47 [WARNING] embedding_service.py:260 - No embedding found for chunk 120
2025-06-25 00:50:47 [INFO] document_service.py:310 - Found 16 chunks for document 98
2025-06-25 00:50:47 [WARNING] embedding_service.py:260 - No embedding found for chunk 121
2025-06-25 00:50:47 [INFO] document_service.py:310 - Found 16 chunks for document 99
2025-06-25 00:50:47 [WARNING] embedding_service.py:260 - No embedding found for chunk 122
2025-06-25 00:50:47 [INFO] document_service.py:310 - Found 16 chunks for document 100
2025-06-25 00:50:47 [WARNING] embedding_service.py:260 - No embedding found for chunk 123
2025-06-25 00:50:47 [INFO] trainprocess_service.py:486 - extract_notes_from_documents len: 100
2025-06-25 00:50:54 [INFO] trainprocess_service.py:508 - Loading user information...
2025-06-25 00:50:54 [INFO] data.py:340 - Subjective notes: 100, Objective notes: 0
2025-06-25 00:50:54 [INFO] data.py:385 - Refined subjective notes: 100
2025-06-25 00:50:54 [INFO] data.py:429 - Refined objective notes: 0
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_79.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_45.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_51.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_86.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_92.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_7.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_6.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_93.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_87.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_50.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_44.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_78.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_remade.json
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_52.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_46.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_91.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_85.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_4.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_5.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_84.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_90.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_47.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_53.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_57.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_43.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_94.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_80.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_1.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_0.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_81.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_95.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_42.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_56.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_40.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_54.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_68.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_83.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_97.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_2.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_3.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_96.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_82.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_69.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_55.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_41.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_26.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_32.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_33.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_27.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_19.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_31.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_25.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_24.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_30.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_18.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_34.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_20.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_21.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_35.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_23.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_37.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_36.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_22.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_13.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_12.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_38.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_10.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_11.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_39.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_15.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_29.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_28.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_14.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_16.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_17.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_58.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_64.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_70.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_71.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_65.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_59.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_73.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_67.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_98.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_99.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_66.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_72.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_76.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_62.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_89.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_8.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_9.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_88.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_63.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_77.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_61.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_75.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_49.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_48.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_74.txt
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/subjective/note_60.txt
2025-06-25 00:50:54 [INFO] data.py:465 - Cleared all existing files in /app/resources/L1/processed_data/subjective
2025-06-25 00:50:54 [INFO] data.py:461 - Removed existing file: /app/resources/L1/processed_data/objective/note_remade.json
2025-06-25 00:50:54 [INFO] data.py:465 - Cleared all existing files in /app/resources/L1/processed_data/objective
2025-06-25 00:50:54 [INFO] data.py:103 - Data refinement completed, preparing to extract entities and relations
2025-06-25 00:50:54 [INFO] data.py:546 - Input base_dir has been updated to /app/resources/L1/processed_data/subjective and saved.
2025-06-25 00:50:54 [INFO] data.py:547 - Output base_dir has been updated to /app/resources/L1/graphrag_indexing_output/subjective and saved.
2025-06-25 00:50:54 [INFO] data.py:548 - Report base_dir has been updated to /app/resources/L1/graphrag_indexing_output/subjective/report and saved.
2025-06-25 00:52:39 [INFO] routes.py:54 - Training process starting...
2025-06-25 00:52:39 [INFO] routes.py:71 - Training parameters: model_name=Qwen2.5-1.5B-Instruct, learning_rate=0.0002, number_of_epochs=5, concurrency_threads=4, data_synthesis_mode=low, is_cot=False
2025-06-25 00:53:04 [INFO] routes.py:54 - Training process starting...
2025-06-25 00:53:04 [INFO] routes.py:71 - Training parameters: model_name=Qwen2.5-1.5B-Instruct, learning_rate=0.0002, number_of_epochs=5, concurrency_threads=4, data_synthesis_mode=low, is_cot=False
