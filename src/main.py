"""
Second Me - Main Application Entry Point
智能靈性助手系統主程序
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
from pathlib import Path

from src.config import settings
from src.api import chat, knowledge, training, health
from src.core.database import init_db
from src.core.model_service import ModelService
from src.core.knowledge_base import KnowledgeBase
from src.utils.logger import setup_logger

# Initialize logger
logger = setup_logger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Second Me - 智能靈性助手",
    description="基於您的靈性智慧和教練學知識的個人AI助手",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
static_path = Path("static")
if static_path.exists():
    app.mount("/static", StaticFiles(directory="static"), name="static")

# Include API routers
app.include_router(health.router, prefix="/api/v1", tags=["health"])
app.include_router(chat.router, prefix="/api/v1", tags=["chat"])
app.include_router(knowledge.router, prefix="/api/v1", tags=["knowledge"])
app.include_router(training.router, prefix="/api/v1", tags=["training"])

# Global dependencies
model_service = None
knowledge_base = None

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    global model_service, knowledge_base
    
    logger.info("Starting Second Me application...")
    
    # Initialize database
    await init_db()
    
    # Initialize model service
    model_service = ModelService(settings.MODEL_CONFIG)
    await model_service.initialize()
    
    # Initialize knowledge base
    knowledge_base = KnowledgeBase(settings.VECTOR_DB_PATH)
    await knowledge_base.initialize()
    
    logger.info("Second Me application started successfully!")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down Second Me application...")
    
    if model_service:
        await model_service.cleanup()
    
    if knowledge_base:
        await knowledge_base.cleanup()
    
    logger.info("Second Me application shut down complete.")

@app.get("/", response_class=HTMLResponse)
async def root():
    """Main page"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Second Me - 智能靈性助手</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; text-align: center; }
            .feature { margin: 20px 0; padding: 20px; background: #ecf0f1; border-radius: 5px; }
            .api-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🌟 Second Me - 智能靈性助手</h1>
            <p>歡迎使用基於您的靈性智慧和教練學知識的個人AI助手系統</p>
            
            <div class="feature">
                <h3>🧠 智能對話</h3>
                <p>與融合了您的靈性智慧的AI進行深度對話</p>
            </div>
            
            <div class="feature">
                <h3>📚 知識庫</h3>
                <p>整合您的豐富內容：靈性文章、教練學資料、WordPress內容</p>
            </div>
            
            <div class="feature">
                <h3>🎯 個性化訓練</h3>
                <p>根據您的風格和知識持續優化AI模型</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="/docs" class="api-link">📖 API文檔</a>
                <a href="/api/v1/health" class="api-link">🔍 系統狀態</a>
            </div>
        </div>
    </body>
    </html>
    """

def get_model_service():
    """Dependency to get model service"""
    if not model_service:
        raise HTTPException(status_code=503, detail="Model service not initialized")
    return model_service

def get_knowledge_base():
    """Dependency to get knowledge base"""
    if not knowledge_base:
        raise HTTPException(status_code=503, detail="Knowledge base not initialized")
    return knowledge_base

# Make dependencies available to other modules
app.dependency_overrides[ModelService] = get_model_service
app.dependency_overrides[KnowledgeBase] = get_knowledge_base

if __name__ == "__main__":
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
