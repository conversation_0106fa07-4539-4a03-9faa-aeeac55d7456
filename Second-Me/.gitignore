__pycache__
.DS_Store
.idea
.ipynb_checkpoints
.pytest_cache
.ruff_cache
.vscode/
.ruff_cache/
poetry.lock
.hf_cache/
.poetry-venv/

llama.cpp
*.ipynb
data/db/*
data/chroma_db/*
data/
lpm_kernel/L2/base_model/
lpm_kernel/L2/data_pipeline/output/
lpm_kernel/L2/data_pipeline/graphrag_indexing/cache/
lpm_kernel/L2/data_pipeline/raw_data/*
!lpm_kernel/L2/data_pipeline/raw_data/.gitkeep
lpm_kernel/L2/data_pipeline/tmp/
lpm_kernel/L2/output_models/

data/sqlite/*
data/uploads/*
data/progress/*
lpm_frontend/node_modules

# L2 Model Storage
resources/model/output/merged_model/*
!resources/model/output/merged_model/.gitkeep
resources/model/output/personal_model/*
!resources/model/output/personal_model/.gitkeep
resources/model/output/*.json
resources/model/output/*.gguf
!resources/model/output/.gitkeep


resources/L1/processed_data/subjective/*
!resources/L1/processed_data/subjective/.gitkeep
resources/L1/processed_data/objective/*
!resources/L1/processed_data/objective/.gitkeep
resources/L1/graphrag_indexing_output/report/*
resources/L1/graphrag_indexing_output/subjective/*


resources/raw_content/*
!resources/raw_content/.gitkeep

# Base model storage address
resources/L2/base_model/*
!resources/L2/base_model/.gitkeep
resources/L2/data_pipeline/raw_data/*
!resources/L2/data_pipeline/raw_data/.gitkeep


resources/model/processed_data/L1/processed_data/objective/*
resources/model/processed_data/L1/processed_data/subjective/*

resources/L2/data/*
!resources/L2/data/.gitkeep

resources/model/output/gguf/*
resources/L2/base_models/*
logs/*.log
logs/train/*.log

# Runtime files
run/*
!run/.gitkeep

#config
.backend.pid
.frontend.pid
logs/train/
llama_cpp_backup/llama.cpp.zip
scripts/check_cuda_status.ps1
scripts/test_cuda_detection.bat
.env
.gpu_selected
