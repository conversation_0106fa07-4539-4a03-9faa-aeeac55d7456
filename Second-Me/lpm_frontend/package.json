{"name": "secondme_frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "rm -rf .next && next dev --turbo -p 3000", "build": "next build", "start": "next start", "eslint:error": "eslint --quiet --ext .js,.jsx,.ts,.tsx src", "eslint:fix": "eslint --fix --ext .js,.jsx,.ts,.tsx src", "eslint": "eslint --ext .js,.jsx,.ts,.tsx src", "stylelint": "stylelint \"src/**/*.less\"", "stylelint:fix": "stylelint \"src/**/*.less\" --fix"}, "lint-staged": {"**/*.{js,jsx,tsx,ts}": ["eslint --quiet --fix"], "**/*.{css,less}": ["stylelint --fix"]}, "overrides": {"braces": "3.0.3", "@babel/runtime": "^7.26.10", "yargs-parser": "^20.2.9", "postcss": "^8.4.31"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@radix-ui/react-slot": "^1.1.2", "@types/styled-components": "^5.1.34", "@types/three": "^0.174.0", "antd": "5.11.0", "axios": "^1.8.2", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "framer-motion": "^12.3.1", "github-markdown-css": "^5.8.1", "lucide-react": "^0.475.0", "next": "14.2.25", "react": "18.3.1", "react-dom": "18.3.1", "react-markdown": "^9.0.3", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.0", "styled-components": "^6.1.15", "tailwind-merge": "^3.0.1", "three": "^0.174.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/canvas-confetti": "^1.9.0", "@types/node": "^20", "@types/react-syntax-highlighter": "^15.5.13", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "eslint": "^8.57.0", "eslint-config-next": "^14.2.13", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.31", "prettier": "^3.1.0", "stylelint": "^15.11.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^34.0.0", "stylelint-order": "^6.0.3", "stylelint-prettier": "^4.1.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}