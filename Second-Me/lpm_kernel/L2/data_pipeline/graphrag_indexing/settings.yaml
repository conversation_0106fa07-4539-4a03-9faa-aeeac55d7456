cache:
  base_dir: cache
  type: file
chunks:
  group_by_columns:
  - id
  overlap: 100
  size: 500
encoding_name: cl100k_base
extract_graph:
  entity_types:
  - organization
  - person
  - geo
  - event
  - specific object
  - abstract object
  max_gleanings: 1
  model_id: default_chat_model
  prompt: prompts/extract_graph.txt
input:
  base_dir: /your_dir
  file_encoding: utf-8
  file_pattern: .*\.txt$$
  file_type: text
  type: file
models:
  default_chat_model:
    api_base: https://api.openai.com/v1
    api_key: sk-xxxxxx
    async_mode: threaded
    auth_type: api_key
    concurrent_requests: 2
    max_retries: -1
    model: gpt-4o-mini
    model_supports_json: true
    requests_per_minute: 0
    retry_strategy: native
    tokens_per_minute: 0
    type: openai_chat
    encoding_model: cl100k_base
  default_embedding_model:
    api_base: https://api.openai.com/v1
    api_key: sk-xxxxxx
    async_mode: threaded
    auth_type: api_key
    concurrent_requests: 2
    max_retries: -1
    model: text-embedding-ada-002
    model_supports_json: true
    requests_per_minute: 0
    retry_strategy: native
    tokens_per_minute: 0
    type: openai_embedding
    encoding_model: cl100k_base
output:
  base_dir: /your_dir
  type: file
reporting:
  base_dir: /your_dir
  type: file
snapshots:
  embeddings: false
  graphml: false
summarize_descriptions:
  max_length: 500
  model_id: default_chat_model
  prompt: prompts/summarize_descriptions.txt
umap:
  enabled: false
workflows:
- create_base_text_units
- create_final_documents
- extract_graph
- finalize_graph