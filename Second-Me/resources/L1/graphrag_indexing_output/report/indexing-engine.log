14:42:59,262 graphrag.cli.index INFO Logging enabled at /app/resources/L1/graphrag_indexing_output/report/indexing-engine.log
14:42:59,262 graphrag.cli.index INFO Starting pipeline run. dry_run=False
14:42:59,262 graphrag.cli.index INFO Using default configuration: {
    "root_dir": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing",
    "models": {
        "default_chat_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_chat",
            "model": "llama3:latest",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "http://host.docker.internal:11434/v1",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": true,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": -1,
            "max_retry_wait": 10.0,
            "concurrent_requests": 2,
            "responses": null,
            "async_mode": "threaded"
        },
        "default_embedding_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_embedding",
            "model": "mxbai-embed-large:latest",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "http://host.docker.internal:11434/v1",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": true,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": -1,
            "max_retry_wait": 10.0,
            "concurrent_requests": 2,
            "responses": null,
            "async_mode": "threaded"
        }
    },
    "reporting": {
        "type": "file",
        "base_dir": "/app/resources/L1/graphrag_indexing_output/report",
        "storage_account_blob_url": null
    },
    "output": {
        "type": "file",
        "base_dir": "/app/resources/L1/graphrag_indexing_output/subjective",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "outputs": null,
    "update_index_output": {
        "type": "file",
        "base_dir": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing/update_output",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "cache": {
        "type": "file",
        "base_dir": "cache",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "input": {
        "type": "file",
        "file_type": "text",
        "base_dir": "/app/resources/L1/processed_data/subjective",
        "storage_account_blob_url": null,
        "encoding": "utf-8",
        "file_pattern": ".*\\.txt$",
        "file_filter": null,
        "text_column": "text",
        "title_column": null,
        "metadata": null
    },
    "embed_graph": {
        "enabled": false,
        "dimensions": 1536,
        "num_walks": 10,
        "walk_length": 40,
        "window_size": 2,
        "iterations": 3,
        "random_seed": 597832,
        "use_lcc": true
    },
    "embed_text": {
        "batch_size": 16,
        "batch_max_tokens": 8191,
        "target": "required",
        "names": [],
        "strategy": null,
        "model_id": "default_embedding_model",
        "vector_store_id": "default_vector_store"
    },
    "chunks": {
        "size": 500,
        "overlap": 100,
        "group_by_columns": [
            "id"
        ],
        "strategy": "tokens",
        "encoding_model": "cl100k_base",
        "prepend_metadata": false,
        "chunk_size_includes_metadata": false
    },
    "snapshots": {
        "embeddings": false,
        "graphml": false
    },
    "extract_graph": {
        "prompt": "prompts/extract_graph.txt",
        "entity_types": [
            "organization",
            "person",
            "geo",
            "event",
            "specific object",
            "abstract object"
        ],
        "max_gleanings": 1,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "extract_graph_nlp": {
        "normalize_edge_weights": true,
        "text_analyzer": {
            "extractor_type": "regex_english",
            "model_name": "en_core_web_md",
            "max_word_length": 15,
            "word_delimiter": " ",
            "include_named_entities": true,
            "exclude_nouns": null,
            "exclude_entity_tags": [
                "DATE"
            ],
            "exclude_pos_tags": [
                "DET",
                "PRON",
                "INTJ",
                "X"
            ],
            "noun_phrase_tags": [
                "PROPN",
                "NOUNS"
            ],
            "noun_phrase_grammars": {
                "PROPN,PROPN": "PROPN",
                "NOUN,NOUN": "NOUNS",
                "NOUNS,NOUN": "NOUNS",
                "ADJ,ADJ": "ADJ",
                "ADJ,NOUN": "NOUNS"
            }
        },
        "concurrent_requests": 25
    },
    "summarize_descriptions": {
        "prompt": "prompts/summarize_descriptions.txt",
        "max_length": 500,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "community_reports": {
        "graph_prompt": null,
        "text_prompt": null,
        "max_length": 2000,
        "max_input_length": 8000,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "extract_claims": {
        "enabled": false,
        "prompt": null,
        "description": "Any claims or facts that could be relevant to information discovery.",
        "max_gleanings": 1,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "prune_graph": {
        "min_node_freq": 2,
        "max_node_freq_std": null,
        "min_node_degree": 1,
        "max_node_degree_std": null,
        "min_edge_weight_pct": 40,
        "remove_ego_nodes": false,
        "lcc_only": false
    },
    "cluster_graph": {
        "max_cluster_size": 10,
        "use_lcc": true,
        "seed": 3735928559
    },
    "umap": {
        "enabled": false
    },
    "local_search": {
        "prompt": null,
        "text_unit_prop": 0.5,
        "community_prop": 0.15,
        "conversation_history_max_turns": 5,
        "top_k_entities": 10,
        "top_k_relationships": 10,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "global_search": {
        "map_prompt": null,
        "reduce_prompt": null,
        "knowledge_prompt": null,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "map_max_tokens": 1000,
        "reduce_max_tokens": 2000,
        "concurrency": 32,
        "dynamic_search_llm": "gpt-4o-mini",
        "dynamic_search_threshold": 1,
        "dynamic_search_keep_parent": false,
        "dynamic_search_num_repeats": 1,
        "dynamic_search_use_summary": false,
        "dynamic_search_concurrent_coroutines": 16,
        "dynamic_search_max_level": 2
    },
    "drift_search": {
        "prompt": null,
        "reduce_prompt": null,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "reduce_max_tokens": 2000,
        "reduce_temperature": 0,
        "concurrency": 32,
        "drift_k_followups": 20,
        "primer_folds": 5,
        "primer_llm_max_tokens": 12000,
        "n_depth": 3,
        "local_search_text_unit_prop": 0.9,
        "local_search_community_prop": 0.1,
        "local_search_top_k_mapped_entities": 10,
        "local_search_top_k_relationships": 10,
        "local_search_max_data_tokens": 12000,
        "local_search_temperature": 0,
        "local_search_top_p": 1,
        "local_search_n": 1,
        "local_search_llm_max_gen_tokens": 2000
    },
    "basic_search": {
        "prompt": null,
        "text_unit_prop": 0.5,
        "conversation_history_max_turns": 5,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "vector_store": {
        "default_vector_store": {
            "type": "lancedb",
            "db_uri": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing/output/lancedb",
            "url": null,
            "audience": null,
            "container_name": "==== REDACTED ====",
            "database_name": null,
            "overwrite": true
        }
    },
    "workflows": [
        "create_base_text_units",
        "create_final_documents",
        "extract_graph",
        "finalize_graph"
    ]
}
14:42:59,263 graphrag.storage.file_pipeline_storage INFO Creating file storage at /app/resources/L1/graphrag_indexing_output/subjective
14:42:59,263 graphrag.index.input.factory INFO loading input from root_dir=/app/resources/L1/processed_data/subjective
14:42:59,264 graphrag.index.input.factory INFO using file storage for input
14:42:59,264 graphrag.storage.file_pipeline_storage INFO search /app/resources/L1/processed_data/subjective for files matching .*\.txt$
14:42:59,265 graphrag.index.input.text INFO found text files from /app/resources/L1/processed_data/subjective, found [('note_79.txt', {}), ('note_45.txt', {}), ('note_51.txt', {}), ('note_86.txt', {}), ('note_92.txt', {}), ('note_7.txt', {}), ('note_6.txt', {}), ('note_93.txt', {}), ('note_87.txt', {}), ('note_50.txt', {}), ('note_44.txt', {}), ('note_78.txt', {}), ('note_52.txt', {}), ('note_46.txt', {}), ('note_91.txt', {}), ('note_85.txt', {}), ('note_4.txt', {}), ('note_5.txt', {}), ('note_84.txt', {}), ('note_90.txt', {}), ('note_47.txt', {}), ('note_53.txt', {}), ('note_57.txt', {}), ('note_43.txt', {}), ('note_94.txt', {}), ('note_80.txt', {}), ('note_1.txt', {}), ('note_0.txt', {}), ('note_81.txt', {}), ('note_95.txt', {}), ('note_42.txt', {}), ('note_56.txt', {}), ('note_40.txt', {}), ('note_54.txt', {}), ('note_68.txt', {}), ('note_83.txt', {}), ('note_97.txt', {}), ('note_2.txt', {}), ('note_3.txt', {}), ('note_96.txt', {}), ('note_82.txt', {}), ('note_69.txt', {}), ('note_55.txt', {}), ('note_41.txt', {}), ('note_26.txt', {}), ('note_32.txt', {}), ('note_33.txt', {}), ('note_27.txt', {}), ('note_19.txt', {}), ('note_31.txt', {}), ('note_25.txt', {}), ('note_24.txt', {}), ('note_30.txt', {}), ('note_18.txt', {}), ('note_34.txt', {}), ('note_20.txt', {}), ('note_21.txt', {}), ('note_35.txt', {}), ('note_23.txt', {}), ('note_37.txt', {}), ('note_36.txt', {}), ('note_22.txt', {}), ('note_13.txt', {}), ('note_12.txt', {}), ('note_38.txt', {}), ('note_10.txt', {}), ('note_11.txt', {}), ('note_39.txt', {}), ('note_15.txt', {}), ('note_29.txt', {}), ('note_28.txt', {}), ('note_14.txt', {}), ('note_16.txt', {}), ('note_17.txt', {}), ('note_58.txt', {}), ('note_64.txt', {}), ('note_70.txt', {}), ('note_71.txt', {}), ('note_65.txt', {}), ('note_59.txt', {}), ('note_73.txt', {}), ('note_67.txt', {}), ('note_98.txt', {}), ('note_99.txt', {}), ('note_66.txt', {}), ('note_72.txt', {}), ('note_76.txt', {}), ('note_62.txt', {}), ('note_89.txt', {}), ('note_8.txt', {}), ('note_9.txt', {}), ('note_88.txt', {}), ('note_63.txt', {}), ('note_77.txt', {}), ('note_61.txt', {}), ('note_75.txt', {}), ('note_49.txt', {}), ('note_48.txt', {}), ('note_74.txt', {}), ('note_60.txt', {})]
14:42:59,311 graphrag.index.input.text INFO Found 100 files, loading 100
14:42:59,312 graphrag.index.run.run_pipeline INFO Final # of rows loaded: 100
14:42:59,330 graphrag.utils.storage INFO reading table from storage: documents.parquet
14:42:59,644 graphrag.utils.storage INFO reading table from storage: documents.parquet
14:42:59,652 graphrag.utils.storage INFO reading table from storage: text_units.parquet
14:42:59,675 graphrag.utils.storage INFO reading table from storage: text_units.parquet
14:44:23,736 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:44:37,102 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:45:42,278 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:46:03,485 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:46:23,397 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:47:15,787 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:47:30,365 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:48:36,971 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:49:27,149 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:49:41,347 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:50:38,701 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:51:08,987 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:51:34,570 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:52:00,822 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:52:47,546 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:53:51,707 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:54:05,70 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:55:47,426 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:56:09,764 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:57:45,212 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:58:47,783 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
14:59:15,629 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:00:08,469 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:00:37,833 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:01:01,440 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:01:21,944 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:01:41,496 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:02:33,621 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:02:42,170 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:03:11,274 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:03:53,227 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:04:47,570 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:04:49,160 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:05:50,677 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:06:10,870 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:06:35,186 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:07:43,157 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:08:09,221 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:09:18,788 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:09:49,227 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:10:43,544 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:10:49,367 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:11:29,626 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:11:51,741 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:12:56,163 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:14:04,904 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:14:29,697 openai._base_client INFO Retrying request to /chat/completions in 0.389080 seconds
15:15:18,604 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:16:45,655 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:17:30,169 openai._base_client INFO Retrying request to /chat/completions in 0.929078 seconds
15:18:16,378 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:19:47,550 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:20:31,177 openai._base_client INFO Retrying request to /chat/completions in 1.759864 seconds
15:20:59,805 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:22:28,40 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:23:25,327 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:23:32,998 openai._base_client INFO Retrying request to /chat/completions in 3.172382 seconds
15:26:04,662 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:26:36,242 openai._base_client INFO Retrying request to /chat/completions in 6.980272 seconds
15:27:27,816 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:29:06,805 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:29:43,295 openai._base_client INFO Retrying request to /chat/completions in 6.125830 seconds
15:30:22,660 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
15:37:37,643 graphrag.cli.index INFO Logging enabled at /app/resources/L1/graphrag_indexing_output/report/indexing-engine.log
15:37:37,645 graphrag.cli.index INFO Starting pipeline run. dry_run=False
15:37:37,645 graphrag.cli.index INFO Using default configuration: {
    "root_dir": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing",
    "models": {
        "default_chat_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_chat",
            "model": "llama3:latest",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "http://host.docker.internal:11434/v1",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": false,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": 3,
            "max_retry_wait": 10.0,
            "concurrent_requests": 1,
            "responses": null,
            "async_mode": "threaded"
        },
        "default_embedding_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_embedding",
            "model": "mxbai-embed-large:latest",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "http://host.docker.internal:11434/v1",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": false,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": 3,
            "max_retry_wait": 10.0,
            "concurrent_requests": 1,
            "responses": null,
            "async_mode": "threaded"
        }
    },
    "reporting": {
        "type": "file",
        "base_dir": "/app/resources/L1/graphrag_indexing_output/report",
        "storage_account_blob_url": null
    },
    "output": {
        "type": "file",
        "base_dir": "/app/resources/L1/graphrag_indexing_output/subjective",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "outputs": null,
    "update_index_output": {
        "type": "file",
        "base_dir": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing/update_output",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "cache": {
        "type": "file",
        "base_dir": "cache",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "input": {
        "type": "file",
        "file_type": "text",
        "base_dir": "/app/resources/L1/processed_data/subjective",
        "storage_account_blob_url": null,
        "encoding": "utf-8",
        "file_pattern": ".*\\.txt12894",
        "file_filter": null,
        "text_column": "text",
        "title_column": null,
        "metadata": null
    },
    "embed_graph": {
        "enabled": false,
        "dimensions": 1536,
        "num_walks": 10,
        "walk_length": 40,
        "window_size": 2,
        "iterations": 3,
        "random_seed": 597832,
        "use_lcc": true
    },
    "embed_text": {
        "batch_size": 16,
        "batch_max_tokens": 8191,
        "target": "required",
        "names": [],
        "strategy": null,
        "model_id": "default_embedding_model",
        "vector_store_id": "default_vector_store"
    },
    "chunks": {
        "size": 500,
        "overlap": 100,
        "group_by_columns": [
            "id"
        ],
        "strategy": "tokens",
        "encoding_model": "cl100k_base",
        "prepend_metadata": false,
        "chunk_size_includes_metadata": false
    },
    "snapshots": {
        "embeddings": false,
        "graphml": false
    },
    "extract_graph": {
        "prompt": "prompts/extract_graph.txt",
        "entity_types": [
            "person",
            "event",
            "concept"
        ],
        "max_gleanings": 0,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "extract_graph_nlp": {
        "normalize_edge_weights": true,
        "text_analyzer": {
            "extractor_type": "regex_english",
            "model_name": "en_core_web_md",
            "max_word_length": 15,
            "word_delimiter": " ",
            "include_named_entities": true,
            "exclude_nouns": null,
            "exclude_entity_tags": [
                "DATE"
            ],
            "exclude_pos_tags": [
                "DET",
                "PRON",
                "INTJ",
                "X"
            ],
            "noun_phrase_tags": [
                "PROPN",
                "NOUNS"
            ],
            "noun_phrase_grammars": {
                "PROPN,PROPN": "PROPN",
                "NOUN,NOUN": "NOUNS",
                "NOUNS,NOUN": "NOUNS",
                "ADJ,ADJ": "ADJ",
                "ADJ,NOUN": "NOUNS"
            }
        },
        "concurrent_requests": 25
    },
    "summarize_descriptions": {
        "prompt": "prompts/summarize_descriptions.txt",
        "max_length": 500,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "community_reports": {
        "graph_prompt": null,
        "text_prompt": null,
        "max_length": 2000,
        "max_input_length": 8000,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "extract_claims": {
        "enabled": false,
        "prompt": null,
        "description": "Any claims or facts that could be relevant to information discovery.",
        "max_gleanings": 1,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "prune_graph": {
        "min_node_freq": 2,
        "max_node_freq_std": null,
        "min_node_degree": 1,
        "max_node_degree_std": null,
        "min_edge_weight_pct": 40,
        "remove_ego_nodes": false,
        "lcc_only": false
    },
    "cluster_graph": {
        "max_cluster_size": 10,
        "use_lcc": true,
        "seed": 3735928559
    },
    "umap": {
        "enabled": false
    },
    "local_search": {
        "prompt": null,
        "text_unit_prop": 0.5,
        "community_prop": 0.15,
        "conversation_history_max_turns": 5,
        "top_k_entities": 10,
        "top_k_relationships": 10,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "global_search": {
        "map_prompt": null,
        "reduce_prompt": null,
        "knowledge_prompt": null,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "map_max_tokens": 1000,
        "reduce_max_tokens": 2000,
        "concurrency": 32,
        "dynamic_search_llm": "gpt-4o-mini",
        "dynamic_search_threshold": 1,
        "dynamic_search_keep_parent": false,
        "dynamic_search_num_repeats": 1,
        "dynamic_search_use_summary": false,
        "dynamic_search_concurrent_coroutines": 16,
        "dynamic_search_max_level": 2
    },
    "drift_search": {
        "prompt": null,
        "reduce_prompt": null,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "reduce_max_tokens": 2000,
        "reduce_temperature": 0,
        "concurrency": 32,
        "drift_k_followups": 20,
        "primer_folds": 5,
        "primer_llm_max_tokens": 12000,
        "n_depth": 3,
        "local_search_text_unit_prop": 0.9,
        "local_search_community_prop": 0.1,
        "local_search_top_k_mapped_entities": 10,
        "local_search_top_k_relationships": 10,
        "local_search_max_data_tokens": 12000,
        "local_search_temperature": 0,
        "local_search_top_p": 1,
        "local_search_n": 1,
        "local_search_llm_max_gen_tokens": 2000
    },
    "basic_search": {
        "prompt": null,
        "text_unit_prop": 0.5,
        "conversation_history_max_turns": 5,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "vector_store": {
        "default_vector_store": {
            "type": "lancedb",
            "db_uri": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing/output/lancedb",
            "url": null,
            "audience": null,
            "container_name": "==== REDACTED ====",
            "database_name": null,
            "overwrite": true
        }
    },
    "workflows": [
        "create_base_text_units",
        "create_final_documents",
        "extract_graph",
        "finalize_graph"
    ]
}
15:37:37,655 asyncio DEBUG Using selector: EpollSelector
15:37:37,656 graphrag.storage.file_pipeline_storage INFO Creating file storage at /app/resources/L1/graphrag_indexing_output/subjective
15:37:37,656 graphrag.index.input.factory INFO loading input from root_dir=/app/resources/L1/processed_data/subjective
15:37:37,656 graphrag.index.input.factory INFO using file storage for input
15:37:37,656 graphrag.storage.file_pipeline_storage INFO search /app/resources/L1/processed_data/subjective for files matching .*\.txt12894
15:41:48,936 graphrag.cli.index INFO Logging enabled at /app/resources/L1/graphrag_indexing_output/report/indexing-engine.log
15:41:48,938 graphrag.cli.index INFO Starting pipeline run. dry_run=False
15:41:48,939 graphrag.cli.index INFO Using default configuration: {
    "root_dir": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing",
    "models": {
        "default_chat_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_chat",
            "model": "llama3:latest",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "http://host.docker.internal:11434/v1",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": false,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": 3,
            "max_retry_wait": 10.0,
            "concurrent_requests": 1,
            "responses": null,
            "async_mode": "threaded"
        },
        "default_embedding_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_embedding",
            "model": "mxbai-embed-large:latest",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "http://host.docker.internal:11434/v1",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": false,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": 3,
            "max_retry_wait": 10.0,
            "concurrent_requests": 1,
            "responses": null,
            "async_mode": "threaded"
        }
    },
    "reporting": {
        "type": "file",
        "base_dir": "/app/resources/L1/graphrag_indexing_output/report",
        "storage_account_blob_url": null
    },
    "output": {
        "type": "file",
        "base_dir": "/app/resources/L1/graphrag_indexing_output/subjective",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "outputs": null,
    "update_index_output": {
        "type": "file",
        "base_dir": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing/update_output",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "cache": {
        "type": "file",
        "base_dir": "cache",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "input": {
        "type": "file",
        "file_type": "text",
        "base_dir": "/app/resources/L1/processed_data/subjective",
        "storage_account_blob_url": null,
        "encoding": "utf-8",
        "file_pattern": ".*\\.txt12894",
        "file_filter": null,
        "text_column": "text",
        "title_column": null,
        "metadata": null
    },
    "embed_graph": {
        "enabled": false,
        "dimensions": 1536,
        "num_walks": 10,
        "walk_length": 40,
        "window_size": 2,
        "iterations": 3,
        "random_seed": 597832,
        "use_lcc": true
    },
    "embed_text": {
        "batch_size": 16,
        "batch_max_tokens": 8191,
        "target": "required",
        "names": [],
        "strategy": null,
        "model_id": "default_embedding_model",
        "vector_store_id": "default_vector_store"
    },
    "chunks": {
        "size": 500,
        "overlap": 100,
        "group_by_columns": [
            "id"
        ],
        "strategy": "tokens",
        "encoding_model": "cl100k_base",
        "prepend_metadata": false,
        "chunk_size_includes_metadata": false
    },
    "snapshots": {
        "embeddings": false,
        "graphml": false
    },
    "extract_graph": {
        "prompt": "prompts/extract_graph.txt",
        "entity_types": [
            "person",
            "event",
            "concept"
        ],
        "max_gleanings": 0,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "extract_graph_nlp": {
        "normalize_edge_weights": true,
        "text_analyzer": {
            "extractor_type": "regex_english",
            "model_name": "en_core_web_md",
            "max_word_length": 15,
            "word_delimiter": " ",
            "include_named_entities": true,
            "exclude_nouns": null,
            "exclude_entity_tags": [
                "DATE"
            ],
            "exclude_pos_tags": [
                "DET",
                "PRON",
                "INTJ",
                "X"
            ],
            "noun_phrase_tags": [
                "PROPN",
                "NOUNS"
            ],
            "noun_phrase_grammars": {
                "PROPN,PROPN": "PROPN",
                "NOUN,NOUN": "NOUNS",
                "NOUNS,NOUN": "NOUNS",
                "ADJ,ADJ": "ADJ",
                "ADJ,NOUN": "NOUNS"
            }
        },
        "concurrent_requests": 25
    },
    "summarize_descriptions": {
        "prompt": "prompts/summarize_descriptions.txt",
        "max_length": 500,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "community_reports": {
        "graph_prompt": null,
        "text_prompt": null,
        "max_length": 2000,
        "max_input_length": 8000,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "extract_claims": {
        "enabled": false,
        "prompt": null,
        "description": "Any claims or facts that could be relevant to information discovery.",
        "max_gleanings": 1,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "prune_graph": {
        "min_node_freq": 2,
        "max_node_freq_std": null,
        "min_node_degree": 1,
        "max_node_degree_std": null,
        "min_edge_weight_pct": 40,
        "remove_ego_nodes": false,
        "lcc_only": false
    },
    "cluster_graph": {
        "max_cluster_size": 10,
        "use_lcc": true,
        "seed": 3735928559
    },
    "umap": {
        "enabled": false
    },
    "local_search": {
        "prompt": null,
        "text_unit_prop": 0.5,
        "community_prop": 0.15,
        "conversation_history_max_turns": 5,
        "top_k_entities": 10,
        "top_k_relationships": 10,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "global_search": {
        "map_prompt": null,
        "reduce_prompt": null,
        "knowledge_prompt": null,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "map_max_tokens": 1000,
        "reduce_max_tokens": 2000,
        "concurrency": 32,
        "dynamic_search_llm": "gpt-4o-mini",
        "dynamic_search_threshold": 1,
        "dynamic_search_keep_parent": false,
        "dynamic_search_num_repeats": 1,
        "dynamic_search_use_summary": false,
        "dynamic_search_concurrent_coroutines": 16,
        "dynamic_search_max_level": 2
    },
    "drift_search": {
        "prompt": null,
        "reduce_prompt": null,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "reduce_max_tokens": 2000,
        "reduce_temperature": 0,
        "concurrency": 32,
        "drift_k_followups": 20,
        "primer_folds": 5,
        "primer_llm_max_tokens": 12000,
        "n_depth": 3,
        "local_search_text_unit_prop": 0.9,
        "local_search_community_prop": 0.1,
        "local_search_top_k_mapped_entities": 10,
        "local_search_top_k_relationships": 10,
        "local_search_max_data_tokens": 12000,
        "local_search_temperature": 0,
        "local_search_top_p": 1,
        "local_search_n": 1,
        "local_search_llm_max_gen_tokens": 2000
    },
    "basic_search": {
        "prompt": null,
        "text_unit_prop": 0.5,
        "conversation_history_max_turns": 5,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "vector_store": {
        "default_vector_store": {
            "type": "lancedb",
            "db_uri": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing/output/lancedb",
            "url": null,
            "audience": null,
            "container_name": "==== REDACTED ====",
            "database_name": null,
            "overwrite": true
        }
    },
    "workflows": [
        "create_base_text_units",
        "create_final_documents",
        "extract_graph",
        "finalize_graph"
    ]
}
15:41:48,947 asyncio DEBUG Using selector: EpollSelector
15:41:48,948 graphrag.storage.file_pipeline_storage INFO Creating file storage at /app/resources/L1/graphrag_indexing_output/subjective
15:41:48,948 graphrag.index.input.factory INFO loading input from root_dir=/app/resources/L1/processed_data/subjective
15:41:48,948 graphrag.index.input.factory INFO using file storage for input
15:41:48,949 graphrag.storage.file_pipeline_storage INFO search /app/resources/L1/processed_data/subjective for files matching .*\.txt12894
18:50:27,57 graphrag.cli.index INFO Logging enabled at /app/resources/L1/graphrag_indexing_output/report/indexing-engine.log
18:50:27,59 graphrag.cli.index INFO Starting pipeline run. dry_run=False
18:50:27,61 graphrag.cli.index INFO Using default configuration: {
    "root_dir": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing",
    "models": {
        "default_chat_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_chat",
            "model": "llama3:latest",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "http://host.docker.internal:11434/v1",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": true,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": 3,
            "max_retry_wait": 10.0,
            "concurrent_requests": 1,
            "responses": null,
            "async_mode": "threaded"
        },
        "default_embedding_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_embedding",
            "model": "mxbai-embed-large:latest",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "http://host.docker.internal:11434/v1",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": null,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": 3,
            "max_retry_wait": 10.0,
            "concurrent_requests": 1,
            "responses": null,
            "async_mode": "threaded"
        }
    },
    "reporting": {
        "type": "file",
        "base_dir": "/app/resources/L1/graphrag_indexing_output/report",
        "storage_account_blob_url": null
    },
    "output": {
        "type": "file",
        "base_dir": "/app/resources/L1/graphrag_indexing_output/subjective",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "outputs": null,
    "update_index_output": {
        "type": "file",
        "base_dir": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing/update_output",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "cache": {
        "type": "file",
        "base_dir": "cache",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "input": {
        "type": "file",
        "file_type": "text",
        "base_dir": "/app/resources/L1/processed_data/subjective",
        "storage_account_blob_url": null,
        "encoding": "utf-8",
        "file_pattern": "*.txt",
        "file_filter": null,
        "text_column": "text",
        "title_column": null,
        "metadata": null
    },
    "embed_graph": {
        "enabled": false,
        "dimensions": 1536,
        "num_walks": 10,
        "walk_length": 40,
        "window_size": 2,
        "iterations": 3,
        "random_seed": 597832,
        "use_lcc": true
    },
    "embed_text": {
        "batch_size": 16,
        "batch_max_tokens": 8191,
        "target": "required",
        "names": [],
        "strategy": null,
        "model_id": "default_embedding_model",
        "vector_store_id": "default_vector_store"
    },
    "chunks": {
        "size": 500,
        "overlap": 100,
        "group_by_columns": [
            "id"
        ],
        "strategy": "tokens",
        "encoding_model": "cl100k_base",
        "prepend_metadata": false,
        "chunk_size_includes_metadata": false
    },
    "snapshots": {
        "embeddings": false,
        "graphml": false
    },
    "extract_graph": {
        "prompt": "prompts/extract_graph.txt",
        "entity_types": [
            "person",
            "event",
            "concept"
        ],
        "max_gleanings": 0,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "extract_graph_nlp": {
        "normalize_edge_weights": true,
        "text_analyzer": {
            "extractor_type": "regex_english",
            "model_name": "en_core_web_md",
            "max_word_length": 15,
            "word_delimiter": " ",
            "include_named_entities": true,
            "exclude_nouns": null,
            "exclude_entity_tags": [
                "DATE"
            ],
            "exclude_pos_tags": [
                "DET",
                "PRON",
                "INTJ",
                "X"
            ],
            "noun_phrase_tags": [
                "PROPN",
                "NOUNS"
            ],
            "noun_phrase_grammars": {
                "PROPN,PROPN": "PROPN",
                "NOUN,NOUN": "NOUNS",
                "NOUNS,NOUN": "NOUNS",
                "ADJ,ADJ": "ADJ",
                "ADJ,NOUN": "NOUNS"
            }
        },
        "concurrent_requests": 25
    },
    "summarize_descriptions": {
        "prompt": "prompts/summarize_descriptions.txt",
        "max_length": 500,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "community_reports": {
        "graph_prompt": null,
        "text_prompt": null,
        "max_length": 2000,
        "max_input_length": 8000,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "extract_claims": {
        "enabled": false,
        "prompt": null,
        "description": "Any claims or facts that could be relevant to information discovery.",
        "max_gleanings": 1,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "prune_graph": {
        "min_node_freq": 2,
        "max_node_freq_std": null,
        "min_node_degree": 1,
        "max_node_degree_std": null,
        "min_edge_weight_pct": 40,
        "remove_ego_nodes": false,
        "lcc_only": false
    },
    "cluster_graph": {
        "max_cluster_size": 10,
        "use_lcc": true,
        "seed": 3735928559
    },
    "umap": {
        "enabled": false
    },
    "local_search": {
        "prompt": null,
        "text_unit_prop": 0.5,
        "community_prop": 0.15,
        "conversation_history_max_turns": 5,
        "top_k_entities": 10,
        "top_k_relationships": 10,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "global_search": {
        "map_prompt": null,
        "reduce_prompt": null,
        "knowledge_prompt": null,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "map_max_tokens": 1000,
        "reduce_max_tokens": 2000,
        "concurrency": 32,
        "dynamic_search_llm": "gpt-4o-mini",
        "dynamic_search_threshold": 1,
        "dynamic_search_keep_parent": false,
        "dynamic_search_num_repeats": 1,
        "dynamic_search_use_summary": false,
        "dynamic_search_concurrent_coroutines": 16,
        "dynamic_search_max_level": 2
    },
    "drift_search": {
        "prompt": null,
        "reduce_prompt": null,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "reduce_max_tokens": 2000,
        "reduce_temperature": 0,
        "concurrency": 32,
        "drift_k_followups": 20,
        "primer_folds": 5,
        "primer_llm_max_tokens": 12000,
        "n_depth": 3,
        "local_search_text_unit_prop": 0.9,
        "local_search_community_prop": 0.1,
        "local_search_top_k_mapped_entities": 10,
        "local_search_top_k_relationships": 10,
        "local_search_max_data_tokens": 12000,
        "local_search_temperature": 0,
        "local_search_top_p": 1,
        "local_search_n": 1,
        "local_search_llm_max_gen_tokens": 2000
    },
    "basic_search": {
        "prompt": null,
        "text_unit_prop": 0.5,
        "conversation_history_max_turns": 5,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "vector_store": {
        "default_vector_store": {
            "type": "lancedb",
            "db_uri": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing/output/lancedb",
            "url": null,
            "audience": null,
            "container_name": "==== REDACTED ====",
            "database_name": null,
            "overwrite": true
        }
    },
    "workflows": [
        "create_base_text_units",
        "create_final_documents",
        "extract_graph",
        "finalize_graph"
    ]
}
18:50:27,70 asyncio DEBUG Using selector: EpollSelector
18:50:27,71 graphrag.storage.file_pipeline_storage INFO Creating file storage at /app/resources/L1/graphrag_indexing_output/subjective
18:50:27,71 graphrag.index.input.factory INFO loading input from root_dir=/app/resources/L1/processed_data/subjective
18:50:27,71 graphrag.index.input.factory INFO using file storage for input
00:15:13,534 graphrag.cli.index INFO Logging enabled at /app/resources/L1/graphrag_indexing_output/report/indexing-engine.log
00:15:13,535 graphrag.cli.index INFO Starting pipeline run. dry_run=False
00:15:13,535 graphrag.cli.index INFO Using default configuration: {
    "root_dir": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing",
    "models": {
        "default_chat_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_chat",
            "model": "llama3:latest",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "http://host.docker.internal:11434/v1",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": true,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": -1,
            "max_retry_wait": 10.0,
            "concurrent_requests": 2,
            "responses": null,
            "async_mode": "threaded"
        },
        "default_embedding_model": {
            "api_key": "==== REDACTED ====",
            "auth_type": "api_key",
            "type": "openai_embedding",
            "model": "mxbai-embed-large:latest",
            "encoding_model": "cl100k_base",
            "max_tokens": 4000,
            "temperature": 0,
            "top_p": 1,
            "n": 1,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "request_timeout": 180.0,
            "api_base": "http://host.docker.internal:11434/v1",
            "api_version": null,
            "deployment_name": null,
            "proxy": null,
            "audience": null,
            "model_supports_json": true,
            "tokens_per_minute": 0,
            "requests_per_minute": 0,
            "retry_strategy": "native",
            "max_retries": -1,
            "max_retry_wait": 10.0,
            "concurrent_requests": 2,
            "responses": null,
            "async_mode": "threaded"
        }
    },
    "reporting": {
        "type": "file",
        "base_dir": "/app/resources/L1/graphrag_indexing_output/report",
        "storage_account_blob_url": null
    },
    "output": {
        "type": "file",
        "base_dir": "/app/resources/L1/graphrag_indexing_output/subjective",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "outputs": null,
    "update_index_output": {
        "type": "file",
        "base_dir": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing/update_output",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "cache": {
        "type": "file",
        "base_dir": "cache",
        "storage_account_blob_url": null,
        "cosmosdb_account_url": null
    },
    "input": {
        "type": "file",
        "file_type": "text",
        "base_dir": "/app/resources/L1/processed_data/subjective",
        "storage_account_blob_url": null,
        "encoding": "utf-8",
        "file_pattern": ".*\\.txt$",
        "file_filter": null,
        "text_column": "text",
        "title_column": null,
        "metadata": null
    },
    "embed_graph": {
        "enabled": false,
        "dimensions": 1536,
        "num_walks": 10,
        "walk_length": 40,
        "window_size": 2,
        "iterations": 3,
        "random_seed": 597832,
        "use_lcc": true
    },
    "embed_text": {
        "batch_size": 16,
        "batch_max_tokens": 8191,
        "target": "required",
        "names": [],
        "strategy": null,
        "model_id": "default_embedding_model",
        "vector_store_id": "default_vector_store"
    },
    "chunks": {
        "size": 500,
        "overlap": 100,
        "group_by_columns": [
            "id"
        ],
        "strategy": "tokens",
        "encoding_model": "cl100k_base",
        "prepend_metadata": false,
        "chunk_size_includes_metadata": false
    },
    "snapshots": {
        "embeddings": false,
        "graphml": false
    },
    "extract_graph": {
        "prompt": "prompts/extract_graph.txt",
        "entity_types": [
            "organization",
            "person",
            "geo",
            "event",
            "specific object",
            "abstract object"
        ],
        "max_gleanings": 1,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "extract_graph_nlp": {
        "normalize_edge_weights": true,
        "text_analyzer": {
            "extractor_type": "regex_english",
            "model_name": "en_core_web_md",
            "max_word_length": 15,
            "word_delimiter": " ",
            "include_named_entities": true,
            "exclude_nouns": null,
            "exclude_entity_tags": [
                "DATE"
            ],
            "exclude_pos_tags": [
                "DET",
                "PRON",
                "INTJ",
                "X"
            ],
            "noun_phrase_tags": [
                "PROPN",
                "NOUNS"
            ],
            "noun_phrase_grammars": {
                "PROPN,PROPN": "PROPN",
                "NOUN,NOUN": "NOUNS",
                "NOUNS,NOUN": "NOUNS",
                "ADJ,ADJ": "ADJ",
                "ADJ,NOUN": "NOUNS"
            }
        },
        "concurrent_requests": 25
    },
    "summarize_descriptions": {
        "prompt": "prompts/summarize_descriptions.txt",
        "max_length": 500,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "community_reports": {
        "graph_prompt": null,
        "text_prompt": null,
        "max_length": 2000,
        "max_input_length": 8000,
        "strategy": null,
        "model_id": "default_chat_model"
    },
    "extract_claims": {
        "enabled": false,
        "prompt": null,
        "description": "Any claims or facts that could be relevant to information discovery.",
        "max_gleanings": 1,
        "strategy": null,
        "encoding_model": null,
        "model_id": "default_chat_model"
    },
    "prune_graph": {
        "min_node_freq": 2,
        "max_node_freq_std": null,
        "min_node_degree": 1,
        "max_node_degree_std": null,
        "min_edge_weight_pct": 40,
        "remove_ego_nodes": false,
        "lcc_only": false
    },
    "cluster_graph": {
        "max_cluster_size": 10,
        "use_lcc": true,
        "seed": 3735928559
    },
    "umap": {
        "enabled": false
    },
    "local_search": {
        "prompt": null,
        "text_unit_prop": 0.5,
        "community_prop": 0.15,
        "conversation_history_max_turns": 5,
        "top_k_entities": 10,
        "top_k_relationships": 10,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "global_search": {
        "map_prompt": null,
        "reduce_prompt": null,
        "knowledge_prompt": null,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "map_max_tokens": 1000,
        "reduce_max_tokens": 2000,
        "concurrency": 32,
        "dynamic_search_llm": "gpt-4o-mini",
        "dynamic_search_threshold": 1,
        "dynamic_search_keep_parent": false,
        "dynamic_search_num_repeats": 1,
        "dynamic_search_use_summary": false,
        "dynamic_search_concurrent_coroutines": 16,
        "dynamic_search_max_level": 2
    },
    "drift_search": {
        "prompt": null,
        "reduce_prompt": null,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "data_max_tokens": 12000,
        "reduce_max_tokens": 2000,
        "reduce_temperature": 0,
        "concurrency": 32,
        "drift_k_followups": 20,
        "primer_folds": 5,
        "primer_llm_max_tokens": 12000,
        "n_depth": 3,
        "local_search_text_unit_prop": 0.9,
        "local_search_community_prop": 0.1,
        "local_search_top_k_mapped_entities": 10,
        "local_search_top_k_relationships": 10,
        "local_search_max_data_tokens": 12000,
        "local_search_temperature": 0,
        "local_search_top_p": 1,
        "local_search_n": 1,
        "local_search_llm_max_gen_tokens": 2000
    },
    "basic_search": {
        "prompt": null,
        "text_unit_prop": 0.5,
        "conversation_history_max_turns": 5,
        "temperature": 0,
        "top_p": 1,
        "n": 1,
        "max_tokens": 12000,
        "llm_max_tokens": 2000
    },
    "vector_store": {
        "default_vector_store": {
            "type": "lancedb",
            "db_uri": "/app/lpm_kernel/L2/data_pipeline/graphrag_indexing/output/lancedb",
            "url": null,
            "audience": null,
            "container_name": "==== REDACTED ====",
            "database_name": null,
            "overwrite": true
        }
    },
    "workflows": [
        "create_base_text_units",
        "create_final_documents",
        "extract_graph",
        "finalize_graph"
    ]
}
00:15:13,536 graphrag.storage.file_pipeline_storage INFO Creating file storage at /app/resources/L1/graphrag_indexing_output/subjective
00:15:13,537 graphrag.index.input.factory INFO loading input from root_dir=/app/resources/L1/processed_data/subjective
00:15:13,537 graphrag.index.input.factory INFO using file storage for input
00:15:13,537 graphrag.storage.file_pipeline_storage INFO search /app/resources/L1/processed_data/subjective for files matching .*\.txt$
00:15:13,538 graphrag.index.input.text INFO found text files from /app/resources/L1/processed_data/subjective, found [('note_79.txt', {}), ('note_45.txt', {}), ('note_51.txt', {}), ('note_86.txt', {}), ('note_92.txt', {}), ('note_7.txt', {}), ('note_6.txt', {}), ('note_93.txt', {}), ('note_87.txt', {}), ('note_50.txt', {}), ('note_44.txt', {}), ('note_78.txt', {}), ('note_52.txt', {}), ('note_46.txt', {}), ('note_91.txt', {}), ('note_85.txt', {}), ('note_4.txt', {}), ('note_5.txt', {}), ('note_84.txt', {}), ('note_90.txt', {}), ('note_47.txt', {}), ('note_53.txt', {}), ('note_57.txt', {}), ('note_43.txt', {}), ('note_94.txt', {}), ('note_80.txt', {}), ('note_1.txt', {}), ('note_0.txt', {}), ('note_81.txt', {}), ('note_95.txt', {}), ('note_42.txt', {}), ('note_56.txt', {}), ('note_40.txt', {}), ('note_54.txt', {}), ('note_68.txt', {}), ('note_83.txt', {}), ('note_97.txt', {}), ('note_2.txt', {}), ('note_3.txt', {}), ('note_96.txt', {}), ('note_82.txt', {}), ('note_69.txt', {}), ('note_55.txt', {}), ('note_41.txt', {}), ('note_26.txt', {}), ('note_32.txt', {}), ('note_33.txt', {}), ('note_27.txt', {}), ('note_19.txt', {}), ('note_31.txt', {}), ('note_25.txt', {}), ('note_24.txt', {}), ('note_30.txt', {}), ('note_18.txt', {}), ('note_34.txt', {}), ('note_20.txt', {}), ('note_21.txt', {}), ('note_35.txt', {}), ('note_23.txt', {}), ('note_37.txt', {}), ('note_36.txt', {}), ('note_22.txt', {}), ('note_13.txt', {}), ('note_12.txt', {}), ('note_38.txt', {}), ('note_10.txt', {}), ('note_11.txt', {}), ('note_39.txt', {}), ('note_15.txt', {}), ('note_29.txt', {}), ('note_28.txt', {}), ('note_14.txt', {}), ('note_16.txt', {}), ('note_17.txt', {}), ('note_58.txt', {}), ('note_64.txt', {}), ('note_70.txt', {}), ('note_71.txt', {}), ('note_65.txt', {}), ('note_59.txt', {}), ('note_73.txt', {}), ('note_67.txt', {}), ('note_98.txt', {}), ('note_99.txt', {}), ('note_66.txt', {}), ('note_72.txt', {}), ('note_76.txt', {}), ('note_62.txt', {}), ('note_89.txt', {}), ('note_8.txt', {}), ('note_9.txt', {}), ('note_88.txt', {}), ('note_63.txt', {}), ('note_77.txt', {}), ('note_61.txt', {}), ('note_75.txt', {}), ('note_49.txt', {}), ('note_48.txt', {}), ('note_74.txt', {}), ('note_60.txt', {})]
00:15:13,586 graphrag.index.input.text INFO Found 100 files, loading 100
00:15:13,590 graphrag.index.run.run_pipeline INFO Final # of rows loaded: 100
00:15:13,630 graphrag.utils.storage INFO reading table from storage: documents.parquet
00:15:14,63 graphrag.utils.storage INFO reading table from storage: documents.parquet
00:15:14,80 graphrag.utils.storage INFO reading table from storage: text_units.parquet
00:15:14,111 graphrag.utils.storage INFO reading table from storage: text_units.parquet
00:17:47,671 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:18:02,864 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:18:44,24 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:19:19,54 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:20:05,18 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:20:14,512 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:21:09,130 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:21:35,148 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:21:58,813 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:22:33,827 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:23:13,731 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:23:14,32 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:24:40,2 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:25:01,712 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:25:46,807 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:26:34,224 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:26:39,628 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:27:55,817 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:28:30,469 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:28:58,911 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:30:02,917 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:30:30,410 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:31:07,358 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:31:51,797 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:32:12,96 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:33:35,273 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:34:21,781 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:34:52,70 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:35:45,854 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:36:14,687 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:36:51,414 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:37:14,155 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:37:47,819 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:38:21,242 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:38:28,94 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:39:02,757 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:39:33,416 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:40:12,90 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:40:39,130 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
00:40:59,286 httpx INFO HTTP Request: POST http://host.docker.internal:11434/v1/chat/completions "HTTP/1.1 200 OK"
