Starting application at Tue Jun 24 08:44:51 UTC 2025
Log directory: /app/logs/train
Log file: /app/logs/app.log
2025-06-24 08:44:51 [INFO] logging.py:36 - Logging system initialized successfully
Log level: 20
Log handlers: ['StreamHandler', 'RotatingFileHandler']
2025-06-24 08:44:51 [INFO] logging.py:53 - Logging module initialization complete
/app/lpm_kernel/utils.py:168: SyntaxWarning: invalid escape sequence '\.'
  sentences = re.split("\. |! |\? |。|！|？|\n+ *\n+", text)
2025-06-24 08:44:59 [INFO] database_session.py:51 - SQLite database engine and session factory initialized
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/usr/local/lib/python3.12/site-packages/flask/__main__.py", line 3, in <module>
    main()
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 1131, in main
    cli.main()
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
         ^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/decorators.py", line 93, in new_func
    return ctx.invoke(f, obj, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 979, in run_command
    raise e from None
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 963, in run_command
    app: WSGIApplication = info.load_app()  # pyright: ignore
                           ^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 349, in load_app
    app = locate_app(import_name, name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 245, in locate_app
    __import__(module_name)
  File "/app/lpm_kernel/app.py", line 4, in <module>
    from .api import init_routes
  File "/app/lpm_kernel/api/__init__.py", line 2, in <module>
    from .domains.health.routes import health_bp
  File "/app/lpm_kernel/api/domains/__init__.py", line 3, in <module>
    from .documents.routes import document_bp
  File "/app/lpm_kernel/api/domains/documents/__init__.py", line 1, in <module>
    from .routes import document_bp
  File "/app/lpm_kernel/api/domains/documents/routes.py", line 11, in <module>
    from lpm_kernel.file_data.document_service import document_service
  File "/app/lpm_kernel/file_data/document_service.py", line 743, in <module>
    document_service = DocumentService()
                       ^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/file_data/document_service.py", line 27, in __init__
    self._insight_kernel = InsightKernel()
                           ^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/kernel/l0_base.py", line 22, in __init__
    self.generator = L0Generator()
                     ^^^^^^^^^^^^^
  File "/app/lpm_kernel/L0/l0_generator.py", line 59, in __init__
    self.user_llm_config = self.user_llm_config_service.get_available_llm()
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/services/user_llm_config_service.py", line 20, in get_available_llm
    return self.repository.get_default_config()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/repositories/user_llm_config_repository.py", line 15, in get_default_config
    return self._get_by_id(1)
           ^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/repositories/user_llm_config_repository.py", line 21, in _get_by_id
    result = session.get(UserLLMConfig, id)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 3694, in get
    return self._get_impl(
           ^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 3873, in _get_impl
    return db_load_fn(
           ^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: user_llm_configs
[SQL: SELECT user_llm_configs.id AS user_llm_configs_id, user_llm_configs.provider_type AS user_llm_configs_provider_type, user_llm_configs."key" AS user_llm_configs_key, user_llm_configs.chat_endpoint AS user_llm_configs_chat_endpoint, user_llm_configs.chat_api_key AS user_llm_configs_chat_api_key, user_llm_configs.chat_model_name AS user_llm_configs_chat_model_name, user_llm_configs.embedding_endpoint AS user_llm_configs_embedding_endpoint, user_llm_configs.embedding_api_key AS user_llm_configs_embedding_api_key, user_llm_configs.embedding_model_name AS user_llm_configs_embedding_model_name, user_llm_configs.thinking_model_name AS user_llm_configs_thinking_model_name, user_llm_configs.thinking_endpoint AS user_llm_configs_thinking_endpoint, user_llm_configs.thinking_api_key AS user_llm_configs_thinking_api_key, user_llm_configs.created_at AS user_llm_configs_created_at, user_llm_configs.updated_at AS user_llm_configs_updated_at 
FROM user_llm_configs 
WHERE user_llm_configs.id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Starting application at Tue Jun 24 08:45:03 UTC 2025
Existing train.log renamed to train_20250624_084503.log
Log directory: /app/logs/train
Log file: /app/logs/app.log
2025-06-24 08:45:03 [INFO] logging.py:36 - Logging system initialized successfully
Log level: 20
Log handlers: ['StreamHandler', 'RotatingFileHandler']
2025-06-24 08:45:03 [INFO] logging.py:53 - Logging module initialization complete
2025-06-24 08:45:05 [INFO] database_session.py:51 - SQLite database engine and session factory initialized
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/usr/local/lib/python3.12/site-packages/flask/__main__.py", line 3, in <module>
    main()
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 1131, in main
    cli.main()
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
         ^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/decorators.py", line 93, in new_func
    return ctx.invoke(f, obj, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 979, in run_command
    raise e from None
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 963, in run_command
    app: WSGIApplication = info.load_app()  # pyright: ignore
                           ^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 349, in load_app
    app = locate_app(import_name, name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 245, in locate_app
    __import__(module_name)
  File "/app/lpm_kernel/app.py", line 4, in <module>
    from .api import init_routes
  File "/app/lpm_kernel/api/__init__.py", line 2, in <module>
    from .domains.health.routes import health_bp
  File "/app/lpm_kernel/api/domains/__init__.py", line 3, in <module>
    from .documents.routes import document_bp
  File "/app/lpm_kernel/api/domains/documents/__init__.py", line 1, in <module>
    from .routes import document_bp
  File "/app/lpm_kernel/api/domains/documents/routes.py", line 11, in <module>
    from lpm_kernel.file_data.document_service import document_service
  File "/app/lpm_kernel/file_data/document_service.py", line 743, in <module>
    document_service = DocumentService()
                       ^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/file_data/document_service.py", line 27, in __init__
    self._insight_kernel = InsightKernel()
                           ^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/kernel/l0_base.py", line 22, in __init__
    self.generator = L0Generator()
                     ^^^^^^^^^^^^^
  File "/app/lpm_kernel/L0/l0_generator.py", line 59, in __init__
    self.user_llm_config = self.user_llm_config_service.get_available_llm()
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/services/user_llm_config_service.py", line 20, in get_available_llm
    return self.repository.get_default_config()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/repositories/user_llm_config_repository.py", line 15, in get_default_config
    return self._get_by_id(1)
           ^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/repositories/user_llm_config_repository.py", line 21, in _get_by_id
    result = session.get(UserLLMConfig, id)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 3694, in get
    return self._get_impl(
           ^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 3873, in _get_impl
    return db_load_fn(
           ^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: user_llm_configs
[SQL: SELECT user_llm_configs.id AS user_llm_configs_id, user_llm_configs.provider_type AS user_llm_configs_provider_type, user_llm_configs."key" AS user_llm_configs_key, user_llm_configs.chat_endpoint AS user_llm_configs_chat_endpoint, user_llm_configs.chat_api_key AS user_llm_configs_chat_api_key, user_llm_configs.chat_model_name AS user_llm_configs_chat_model_name, user_llm_configs.embedding_endpoint AS user_llm_configs_embedding_endpoint, user_llm_configs.embedding_api_key AS user_llm_configs_embedding_api_key, user_llm_configs.embedding_model_name AS user_llm_configs_embedding_model_name, user_llm_configs.thinking_model_name AS user_llm_configs_thinking_model_name, user_llm_configs.thinking_endpoint AS user_llm_configs_thinking_endpoint, user_llm_configs.thinking_api_key AS user_llm_configs_thinking_api_key, user_llm_configs.created_at AS user_llm_configs_created_at, user_llm_configs.updated_at AS user_llm_configs_updated_at 
FROM user_llm_configs 
WHERE user_llm_configs.id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Starting application at Tue Jun 24 08:45:06 UTC 2025
Existing train.log renamed to train_20250624_084506.log
Log directory: /app/logs/train
Log file: /app/logs/app.log
2025-06-24 08:45:06 [INFO] logging.py:36 - Logging system initialized successfully
Log level: 20
Log handlers: ['StreamHandler', 'RotatingFileHandler']
2025-06-24 08:45:06 [INFO] logging.py:53 - Logging module initialization complete
2025-06-24 08:45:08 [INFO] database_session.py:51 - SQLite database engine and session factory initialized
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/usr/local/lib/python3.12/site-packages/flask/__main__.py", line 3, in <module>
    main()
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 1131, in main
    cli.main()
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
         ^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/decorators.py", line 93, in new_func
    return ctx.invoke(f, obj, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 979, in run_command
    raise e from None
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 963, in run_command
    app: WSGIApplication = info.load_app()  # pyright: ignore
                           ^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 349, in load_app
    app = locate_app(import_name, name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 245, in locate_app
    __import__(module_name)
  File "/app/lpm_kernel/app.py", line 4, in <module>
    from .api import init_routes
  File "/app/lpm_kernel/api/__init__.py", line 2, in <module>
    from .domains.health.routes import health_bp
  File "/app/lpm_kernel/api/domains/__init__.py", line 3, in <module>
    from .documents.routes import document_bp
  File "/app/lpm_kernel/api/domains/documents/__init__.py", line 1, in <module>
    from .routes import document_bp
  File "/app/lpm_kernel/api/domains/documents/routes.py", line 11, in <module>
    from lpm_kernel.file_data.document_service import document_service
  File "/app/lpm_kernel/file_data/document_service.py", line 743, in <module>
    document_service = DocumentService()
                       ^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/file_data/document_service.py", line 27, in __init__
    self._insight_kernel = InsightKernel()
                           ^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/kernel/l0_base.py", line 22, in __init__
    self.generator = L0Generator()
                     ^^^^^^^^^^^^^
  File "/app/lpm_kernel/L0/l0_generator.py", line 59, in __init__
    self.user_llm_config = self.user_llm_config_service.get_available_llm()
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/services/user_llm_config_service.py", line 20, in get_available_llm
    return self.repository.get_default_config()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/repositories/user_llm_config_repository.py", line 15, in get_default_config
    return self._get_by_id(1)
           ^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/repositories/user_llm_config_repository.py", line 21, in _get_by_id
    result = session.get(UserLLMConfig, id)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 3694, in get
    return self._get_impl(
           ^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 3873, in _get_impl
    return db_load_fn(
           ^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: user_llm_configs
[SQL: SELECT user_llm_configs.id AS user_llm_configs_id, user_llm_configs.provider_type AS user_llm_configs_provider_type, user_llm_configs."key" AS user_llm_configs_key, user_llm_configs.chat_endpoint AS user_llm_configs_chat_endpoint, user_llm_configs.chat_api_key AS user_llm_configs_chat_api_key, user_llm_configs.chat_model_name AS user_llm_configs_chat_model_name, user_llm_configs.embedding_endpoint AS user_llm_configs_embedding_endpoint, user_llm_configs.embedding_api_key AS user_llm_configs_embedding_api_key, user_llm_configs.embedding_model_name AS user_llm_configs_embedding_model_name, user_llm_configs.thinking_model_name AS user_llm_configs_thinking_model_name, user_llm_configs.thinking_endpoint AS user_llm_configs_thinking_endpoint, user_llm_configs.thinking_api_key AS user_llm_configs_thinking_api_key, user_llm_configs.created_at AS user_llm_configs_created_at, user_llm_configs.updated_at AS user_llm_configs_updated_at 
FROM user_llm_configs 
WHERE user_llm_configs.id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Starting application at Tue Jun 24 08:45:09 UTC 2025
Existing train.log renamed to train_20250624_084510.log
Log directory: /app/logs/train
Log file: /app/logs/app.log
2025-06-24 08:45:10 [INFO] logging.py:36 - Logging system initialized successfully
Log level: 20
Log handlers: ['StreamHandler', 'RotatingFileHandler']
2025-06-24 08:45:10 [INFO] logging.py:53 - Logging module initialization complete
2025-06-24 08:45:12 [INFO] database_session.py:51 - SQLite database engine and session factory initialized
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/usr/local/lib/python3.12/site-packages/flask/__main__.py", line 3, in <module>
    main()
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 1131, in main
    cli.main()
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
         ^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/decorators.py", line 93, in new_func
    return ctx.invoke(f, obj, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 979, in run_command
    raise e from None
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 963, in run_command
    app: WSGIApplication = info.load_app()  # pyright: ignore
                           ^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 349, in load_app
    app = locate_app(import_name, name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 245, in locate_app
    __import__(module_name)
  File "/app/lpm_kernel/app.py", line 4, in <module>
    from .api import init_routes
  File "/app/lpm_kernel/api/__init__.py", line 2, in <module>
    from .domains.health.routes import health_bp
  File "/app/lpm_kernel/api/domains/__init__.py", line 3, in <module>
    from .documents.routes import document_bp
  File "/app/lpm_kernel/api/domains/documents/__init__.py", line 1, in <module>
    from .routes import document_bp
  File "/app/lpm_kernel/api/domains/documents/routes.py", line 11, in <module>
    from lpm_kernel.file_data.document_service import document_service
  File "/app/lpm_kernel/file_data/document_service.py", line 743, in <module>
    document_service = DocumentService()
                       ^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/file_data/document_service.py", line 27, in __init__
    self._insight_kernel = InsightKernel()
                           ^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/kernel/l0_base.py", line 22, in __init__
    self.generator = L0Generator()
                     ^^^^^^^^^^^^^
  File "/app/lpm_kernel/L0/l0_generator.py", line 59, in __init__
    self.user_llm_config = self.user_llm_config_service.get_available_llm()
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/services/user_llm_config_service.py", line 20, in get_available_llm
    return self.repository.get_default_config()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/repositories/user_llm_config_repository.py", line 15, in get_default_config
    return self._get_by_id(1)
           ^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/repositories/user_llm_config_repository.py", line 21, in _get_by_id
    result = session.get(UserLLMConfig, id)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 3694, in get
    return self._get_impl(
           ^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 3873, in _get_impl
    return db_load_fn(
           ^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: user_llm_configs
[SQL: SELECT user_llm_configs.id AS user_llm_configs_id, user_llm_configs.provider_type AS user_llm_configs_provider_type, user_llm_configs."key" AS user_llm_configs_key, user_llm_configs.chat_endpoint AS user_llm_configs_chat_endpoint, user_llm_configs.chat_api_key AS user_llm_configs_chat_api_key, user_llm_configs.chat_model_name AS user_llm_configs_chat_model_name, user_llm_configs.embedding_endpoint AS user_llm_configs_embedding_endpoint, user_llm_configs.embedding_api_key AS user_llm_configs_embedding_api_key, user_llm_configs.embedding_model_name AS user_llm_configs_embedding_model_name, user_llm_configs.thinking_model_name AS user_llm_configs_thinking_model_name, user_llm_configs.thinking_endpoint AS user_llm_configs_thinking_endpoint, user_llm_configs.thinking_api_key AS user_llm_configs_thinking_api_key, user_llm_configs.created_at AS user_llm_configs_created_at, user_llm_configs.updated_at AS user_llm_configs_updated_at 
FROM user_llm_configs 
WHERE user_llm_configs.id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Starting application at Tue Jun 24 08:45:13 UTC 2025
Existing train.log renamed to train_20250624_084513.log
Log directory: /app/logs/train
Log file: /app/logs/app.log
2025-06-24 08:45:13 [INFO] logging.py:36 - Logging system initialized successfully
Log level: 20
Log handlers: ['StreamHandler', 'RotatingFileHandler']
2025-06-24 08:45:13 [INFO] logging.py:53 - Logging module initialization complete
2025-06-24 08:45:15 [INFO] database_session.py:51 - SQLite database engine and session factory initialized
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/usr/local/lib/python3.12/site-packages/flask/__main__.py", line 3, in <module>
    main()
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 1131, in main
    cli.main()
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
         ^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/decorators.py", line 93, in new_func
    return ctx.invoke(f, obj, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 979, in run_command
    raise e from None
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 963, in run_command
    app: WSGIApplication = info.load_app()  # pyright: ignore
                           ^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 349, in load_app
    app = locate_app(import_name, name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/flask/cli.py", line 245, in locate_app
    __import__(module_name)
  File "/app/lpm_kernel/app.py", line 4, in <module>
    from .api import init_routes
  File "/app/lpm_kernel/api/__init__.py", line 2, in <module>
    from .domains.health.routes import health_bp
  File "/app/lpm_kernel/api/domains/__init__.py", line 3, in <module>
    from .documents.routes import document_bp
  File "/app/lpm_kernel/api/domains/documents/__init__.py", line 1, in <module>
    from .routes import document_bp
  File "/app/lpm_kernel/api/domains/documents/routes.py", line 11, in <module>
    from lpm_kernel.file_data.document_service import document_service
  File "/app/lpm_kernel/file_data/document_service.py", line 743, in <module>
    document_service = DocumentService()
                       ^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/file_data/document_service.py", line 27, in __init__
    self._insight_kernel = InsightKernel()
                           ^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/kernel/l0_base.py", line 22, in __init__
    self.generator = L0Generator()
                     ^^^^^^^^^^^^^
  File "/app/lpm_kernel/L0/l0_generator.py", line 59, in __init__
    self.user_llm_config = self.user_llm_config_service.get_available_llm()
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/services/user_llm_config_service.py", line 20, in get_available_llm
    return self.repository.get_default_config()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/repositories/user_llm_config_repository.py", line 15, in get_default_config
    return self._get_by_id(1)
           ^^^^^^^^^^^^^^^^^^
  File "/app/lpm_kernel/api/repositories/user_llm_config_repository.py", line 21, in _get_by_id
    result = session.get(UserLLMConfig, id)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 3694, in get
    return self._get_impl(
           ^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 3873, in _get_impl
    return db_load_fn(
           ^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/orm/context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "/usr/local/lib/python3.12/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: user_llm_configs
[SQL: SELECT user_llm_configs.id AS user_llm_configs_id, user_llm_configs.provider_type AS user_llm_configs_provider_type, user_llm_configs."key" AS user_llm_configs_key, user_llm_configs.chat_endpoint AS user_llm_configs_chat_endpoint, user_llm_configs.chat_api_key AS user_llm_configs_chat_api_key, user_llm_configs.chat_model_name AS user_llm_configs_chat_model_name, user_llm_configs.embedding_endpoint AS user_llm_configs_embedding_endpoint, user_llm_configs.embedding_api_key AS user_llm_configs_embedding_api_key, user_llm_configs.embedding_model_name AS user_llm_configs_embedding_model_name, user_llm_configs.thinking_model_name AS user_llm_configs_thinking_model_name, user_llm_configs.thinking_endpoint AS user_llm_configs_thinking_endpoint, user_llm_configs.thinking_api_key AS user_llm_configs_thinking_api_key, user_llm_configs.created_at AS user_llm_configs_created_at, user_llm_configs.updated_at AS user_llm_configs_updated_at 
FROM user_llm_configs 
WHERE user_llm_configs.id = ?]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
