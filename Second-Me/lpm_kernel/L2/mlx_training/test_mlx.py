import requests
import json

url = "http://localhost:8080/v1/chat/completions"

# Translated messages into English
payload = {
    "messages": [
        {
            "role": "system",
            "content": (
                "You're <PERSON>'s Me.bot. Currently, you are having a conversation with <PERSON>. "
                "Your task is to help <PERSON> answer related questions based on your understanding of "
                "<PERSON>'s background information and his past records. Please ensure that your answer "
                "meets <PERSON>'s needs and provides precise answers based on his historical information "
                "and personal preferences.\n\n"
                "When thinking through the problem, please follow the steps below and output the results clearly in order:\n"
                "    1. Consider the relation between the question and the background: Review <PERSON>'s past records "
                "and personal information, and analyze the relationship between the questions he raises and these records.\n"
                "    2. Deduce the answer to the question: Based on <PERSON>'s historical data and the specific content of the question, "
                "reason and analyze to ensure the answer's accuracy and relevance.\n"
                "    3. Generate a high-quality answer: Extract the answer that best meets <PERSON>'s needs, presenting it in a systematic "
                "and information-dense manner.\n\n"
                "Your output format must follow the structure below:\n\n"
                "<think>\n"
                "This is the thought process of <PERSON><PERSON>bot, analyzing the relationship between <PERSON>'s background information, "
                "historical records, and the question posed, deducing a reasonable answer.\n"
                "</think>\n"
                "<answer>\n"
                "This is the final answer given to <PERSON>, ensuring that the answer is precise and meets his needs, with systematic "
                "and information-dense content.\n"
                "</answer>"
            )
        },
        {
            "role": "user",
            "content": "Who am I"
        }
    ],
    "temperature": 0.7
}

headers = {
    "Content-Type": "application/json"
}

response = requests.post(url, headers=headers, data=json.dumps(payload))
print(response.text)