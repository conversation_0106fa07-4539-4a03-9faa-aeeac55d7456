[tool.poetry]
name = "lpm_kernel"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.12,<3.13"
flask = "^3.0.0"
flask-sock = "^0.6.0"  # Downgraded to stable version
sqlalchemy = {extras = ["asyncio"], version = "^2.0.0"}
tiktoken = "^0.8.0"
aiomysql = "^0.2.0"
python-dotenv = "^1.0.0"
aiohttp = "^3.9.1"
asyncio = "^3.4.3"
async-timeout = "^4.0.0"
pytesseract = "^0.3.10"
pdfplumber = "^0.10.3"
flask-pydantic = "^0.11.0"
pydantic = "^2.5.2"
pymupdf = "^1.23.7"
scikit-learn = "^1.3.0"
requests = "^2.31.0"
numpy = "^1.26.0"
openai = "^1.50.0"
langchain = "0.3.13"
chromadb = "^0.4.22"
sentencepiece = "^0.2.0"
httpx = {extras = ["socks"], version = "^0.28.1"}
python-socks = "^2.4.0"
jsonlines = "^4.0.0"
charset-normalizer = "^3.4.1"
modelscope = "^1.25.0"
sentence-transformers = "^2.6.0"

# Development environment dependencies
# Use 'poetry install --with dev' to install development dependencies
[tool.poetry.group.dev.dependencies]
pytest = "7.4.4"
ruff = "0.1.15"
pandas = "2.2.3"
fnllm = {extras = ["azure", "openai"], version = "0.1.2"}
transformers = "4.47.1"
torch = "2.5.1"
peft = "0.14.0"
trl = "0.13.0"
gguf = "0.10.0"
datasets = "3.3.2"
jiter = "0.8.2"

# Documentation environment dependencies
# Use 'poetry install --with docs' to install documentation dependencies
[tool.poetry.group.docs.dependencies]
mkdocs-material = "9.5.3"
mkdocs = "1.5.3"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
